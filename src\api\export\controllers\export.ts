import * as fs from "fs";
import * as fsPromises from "fs/promises";
import * as path from "path";
import archiver from "archiver";
import { Workbook } from "exceljs";
import { Readable } from "stream";
import { AsyncParser } from "@json2csv/node";
import { Context } from "koa";
import { PassThrough } from "stream";
import { State } from "country-state-city";
import {
  createCsvStreamForFGControlMain,
  processFGControlMainToStream,
} from "../../fg-control-main/helpers";
import {
  createProductCsvStream,
  createProductSpecificationCsvStream,
  processProductSpecificationsToStream,
  processProductWebAttributesToStream,
} from "../../product/helpers";

const exportDataAsCSV = async (ctx: Context) => {
  try {
    const data = ctx.request.body || [];

    if (!Array.isArray(data) || data.length === 0) {
      return ctx.badRequest("Invalid or empty data");
    }

    const parser = new AsyncParser();
    const csv = await parser.parse(data).promise();

    ctx.set("Content-Type", "text/csv");
    ctx.set("Content-Disposition", 'attachment; filename="cart.csv"');

    ctx.send(csv);
  } catch (error) {
    ctx.throw(500, "Failed to generate CSV");
  }
};

const modelConfigs = {
  "business-partner": {
    query: {
      fields: ["bp_id", "bp_full_name", "is_marked_for_archiving"],
      populate: {
        addresses: {
          fields: [
            "house_number",
            "street_name",
            "city_name",
            "region",
            "country",
            "postal_code",
          ],
          populate: {
            address_usages: { fields: ["address_usage"] },
            emails: { fields: ["email_address"] },
            phone_numbers: { fields: ["phone_number", "phone_number_type"] },
          },
        },
        contact_companies: {
          populate: {
            business_partner_person: {
              fields: ["first_name", "last_name"],
            },
          },
        },
        customer: {
          fields: ["id"],
          populate: {
            partner_functions: {
              fields: ["partner_function"],
              populate: {
                business_partner: { fields: ["bp_full_name"] },
              },
            },
          },
        },
      },
    },
    transform: (record, context) => {
      const getStateNameByCode = (
        stateCode: string,
        countryCode: string
      ): string => {
        const states = State.getStatesOfCountry(countryCode);
        const match = states.find((state) => state.isoCode === stateCode);
        return match ? match.name : "";
      };
      const defaultAddress = record.addresses?.find((address) =>
        address.address_usages?.find(
          (usage) => usage.address_usage === "XXDEFAULT"
        )
      );
      const partner_role = record?.customer?.partner_functions?.find(
        (p: any) => p.partner_function === "YI"
      );

      // Determine if it's an account or prospect based on the roles filter from context
      const isAccount =
        context?.filters?.roles?.bp_role?.$in?.includes("FLCU01") ||
        context?.filters?.roles?.bp_role?.$in?.includes("FLCU00");
      const isProspect =
        context?.filters?.roles?.bp_role?.$in?.includes("PRO001");

      if (isAccount) {
        return {
          account_id: record?.bp_id || "",
          name: record?.bp_full_name || "",
          address: [
            defaultAddress?.house_number,
            defaultAddress?.street_name,
            defaultAddress?.city_name,
            defaultAddress?.region,
            defaultAddress?.country,
            defaultAddress?.postal_code,
          ]
            .filter(Boolean)
            .join(", "),
          city_name: defaultAddress?.city_name || "",
          region:
            getStateNameByCode(
              defaultAddress?.region,
              defaultAddress?.country
            ) || "",
          country: defaultAddress?.country || "",
          email: defaultAddress?.emails?.[0]?.email_address || "",
          phone:
            defaultAddress?.phone_numbers?.find(
              (p) => p.phone_number_type === "3"
            )?.phone_number || "",
          contact: [
            record.contact_companies?.[0]?.business_partner_person?.first_name,
            record.contact_companies?.[0]?.business_partner_person?.last_name,
          ]
            .filter(Boolean)
            .join(" "),
        };
      } else if (isProspect) {
        return {
          prospect_id: record?.bp_id || "",
          name: record?.bp_full_name || "",
          contact_name:
            [
              record?.contact_companies?.[0]?.business_partner_person
                ?.first_name,
              record?.contact_companies?.[0]?.business_partner_person
                ?.last_name,
            ]
              .filter(Boolean)
              .join(" ") || "",
          city_name: defaultAddress?.city_name || "",
          country: defaultAddress?.country || "",
          email_address: defaultAddress?.emails?.[0]?.email_address || "",
          phone_number:
            (defaultAddress?.phone_numbers || []).find(
              (item: any) => item.phone_number_type === "1"
            )?.phone_number || "",
          owner: partner_role?.business_partner?.bp_full_name || "",
        };
      }
      return record;
    },
  },
  "business-partner-contact": {
    query: {
      fields: ["bp_company_id", "bp_person_id"],
      populate: {
        business_partner_company: { fields: ["bp_full_name"] },
        business_partner_person: {
          fields: ["bp_full_name", "is_marked_for_archiving"],
          populate: {
            addresses: {
              fields: [
                "house_number",
                "street_name",
                "city_name",
                "region",
                "country",
                "postal_code",
              ],
              populate: {
                emails: { fields: ["email_address"] },
                phone_numbers: {
                  fields: ["phone_number", "phone_number_type"],
                },
              },
            },
            bp_extension: { fields: ["web_registered", "business_department"] },
          },
        },
        person_func_and_dept: { fields: ["contact_person_department"] },
      },
    },
    transform: (record) => {
      const addresses = record?.business_partner_person?.addresses || [];
      const getPhoneNumberByType = (type: string) =>
        (addresses?.[0]?.phone_numbers || [])
          .filter((item: any) => item.phone_number_type === type)
          .map((item: any) => item.phone_number)
          .join("");

      return {
        contact_id: record?.bp_person_id || "",
        contact_name: record?.business_partner_person?.bp_full_name || "",
        account_name: record?.business_partner_company?.bp_full_name || "",
        business_department:
          record?.person_func_and_dept?.contact_person_department || "",
        email_address: addresses?.[0]?.emails?.[0]?.email_address || "",
        phone_number: getPhoneNumberByType("1"),
        mobile: getPhoneNumberByType("3"),
        web_registered: record?.business_partner_person?.bp_extension
          ?.web_registered
          ? true
          : false,
        status: record?.business_partner_person?.is_marked_for_archiving
          ? "Obsolete"
          : "Active",
        account_id: record?.bp_company_id || "-",
      };
    },
  },
  "crm-activity": {
    query: {
      fields: [
        "main_account_party_id",
        "activity_id",
        "subject",
        "owner_party_id",
        "brand",
        "customer_group",
        "ranking",
        "activity_status",
        "phone_call_category",
        "customer_timezone",
        "createdAt",
        "updatedAt",
        "document_type",
        "priority",
        "processor_party_id",
        "task_category",
        "start_date",
        "end_date",
      ],
      populate: {
        notes: { fields: ["note", "is_global_note"] },
        business_partner: {
          fields: ["bp_full_name"],
          populate: {
            addresses: { fields: ["region", "country"] },
          },
        },
        business_partner_contact: { fields: ["bp_full_name"] },
        business_partner_processor: { fields: ["bp_full_name"] },
        business_partner_owner: { fields: ["bp_full_name"] },
      },
    },
    transform: (record) => {
      const getStateNameByCode = (
        stateCode: string,
        countryCode: string
      ): string => {
        const states = State.getStatesOfCountry(countryCode);
        const match = states.find((state) => state.isoCode === stateCode);
        return match ? match.name : "";
      };

      // Handle transformation based on document_type
      const isPhoneCall = record.document_type === "0002";
      const isTask = record.document_type === "0006";
      const state = getStateNameByCode(
        record?.business_partner?.addresses?.[0]?.region,
        record?.business_partner?.addresses?.[0]?.country
      );
      const globalNote = record.notes?.find((note: any) => note.is_global_note);

      if (isPhoneCall) {
        return {
          account_id: record?.main_account_party_id || "",
          activity_id: record?.activity_id || "",
          subject: record?.subject || "",
          account: record?.business_partner?.bp_full_name || "",
          ranking: record?.ranking || "",
          state: state || "",
          note: globalNote?.note || "",
          customer_group: record?.customer_group || "",
          brand: record?.brand || "",
          category: record?.phone_call_category || "",
          status: record?.activity_status || "",
          created_at: record?.createdAt || "",
          owner: record?.business_partner_owner?.bp_full_name || "",
          customer_timezone: record?.customer_timezone || "",
        };
      } else if (isTask) {
        return {
          account_id: record?.main_account_party_id || "",
          activity_id: record?.activity_id || "",
          subject: record?.subject || "",
          account: record?.business_partner?.bp_full_name || "",
          contact: record?.business_partner_contact?.bp_full_name || "",
          status: record?.activity_status || "",
          note: globalNote?.note || "",
          start_date: record?.start_date || "",
          end_date: record?.end_date || "",
          priority: record?.priority || "",
          processor: record?.business_partner_processor?.bp_full_name || "",
          owner: record?.business_partner_owner?.bp_full_name || "",
        };
      }
      // Fallback for unhandled document types
      return {
        account_id: record?.main_account_party_id || "",
        activity_id: record?.activity_id || "",
        subject: record?.subject || "",
        account: record?.business_partner?.bp_full_name || "",
        activity_status: record?.activity_status || "",
      };
    },
  },
  "crm-opportunity": {
    query: {
      fields: [
        "opportunity_id",
        "name",
        "expected_revenue_amount",
        "life_cycle_status_code",
        "probability_percent",
        "expected_revenue_start_date",
        "expected_revenue_end_date",
        "updatedAt",
        "last_changed_by",
      ],
      populate: {
        business_partner: { fields: ["bp_full_name"] },
        business_partner_owner: { fields: ["bp_full_name"] },
      },
    },
    transform: (record) => {
      return {
        opportunity_id: record?.opportunity_id || "",
        name: record?.name || "",
        account: record?.business_partner?.bp_full_name || "",
        owner: record?.business_partner_owner?.bp_full_name || "",
        start_date: record?.expected_revenue_start_date || "",
        end_date: record?.expected_revenue_end_date || "",
        updated_at: record?.updatedAt || "",
        last_changed_by: record?.last_changed_by || "",
        amount: record?.expected_revenue_amount || "",
        status: record?.life_cycle_status_code || "",
        probability: record?.probability_percent || "",
        need_help: "",
      };
    },
  },
  "fg-control-main": { query: {}, transform: (record) => record },
  "fg-customer-business": { query: {}, transform: (record) => record },
  "fg-customer-internal": { query: {}, transform: (record) => record },
  "fg-product-business": { query: {}, transform: (record) => record },
  "fg-product-internal": { query: {}, transform: (record) => record },
  "fg-relationship": { query: {}, transform: (record) => record },
  "import-file-log": { query: {}, transform: (record) => record },
};

const fetchRecords = async (
  modelName: string,
  filters: any,
  start: number,
  limit: number
) => {
  const config = modelConfigs[modelName] || {};
  return await strapi.documents(`api::${modelName}.${modelName}`).findMany({
    filters,
    limit,
    start,
    ...config.query,
  });
};

const exportData = async (ctx: any, tableName: string, filters?: any) => {
  const modelName = tableName.toLowerCase().replace(/_/g, "-");
  const exportDir = path.resolve("exports");
  const uniqueId = Date.now();
  const fileName = `${modelName}-${uniqueId}`;
  const zipFile = path.join(exportDir, `${fileName}.zip`);
  const batchSize = 1000;

  try {
    // Ensure the directory exists
    await fsPromises.mkdir(exportDir, { recursive: true });

    // Create a writable stream for the ZIP file
    const zipStream = fs.createWriteStream(zipFile);
    const archive = archiver("zip", { zlib: { level: 9 } });

    archive.pipe(zipStream);

    // Create a new Excel workbook and worksheet
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet("Data Export");

    let start = 0;
    let hasMore = true;

    while (hasMore) {
      const records = await fetchRecords(modelName, filters, start, batchSize);

      // Safely handle transform with optional context
      const transform = (record) => {
        const transformFn = modelConfigs[modelName]?.transform;
        if (transformFn && transformFn.length > 1) {
          return transformFn(record, { filters }) || record;
        }
        return transformFn ? transformFn(record) : record;
      };

      if (start === 0 && records.length > 0) {
        const excludeField: string[] = [
          "documentId",
          "updatedAt",
          "publishedAt",
          "createdBy",
          "updatedBy",
          "locale",
          "localizations",
        ];

        switch (modelName) {
          case "fg-control-main":
          case "fg-customer-business":
          case "fg-product-business":
          case "fg-relationship":
            excludeField.push("id");
            excludeField.push("createdAt");
            break;
          default:
        }

        const transformedRecords = records.map(transform);

        const headers = Object.keys(transformedRecords[0]).filter(
          (field) => !excludeField.includes(field)
        );
        worksheet.columns = headers.map((header) => ({
          header,
          key: header,
          width: 20,
        }));

        transformedRecords.forEach((record) => worksheet.addRow(record));
      } else if (records.length > 0) {
        const transformedRecords = records.map(transform);
        transformedRecords.forEach((record) => worksheet.addRow(record));
      }

      hasMore = records.length === batchSize;
      start += batchSize;
    }

    // Create a buffer for the Excel file content
    const excelBuffer = await workbook.xlsx.writeBuffer();

    // Convert the buffer to a readable stream and pipe it to the zip archive
    const bufferStream = new Readable();
    bufferStream.push(excelBuffer);
    bufferStream.push(null); // End the stream

    // Append the buffer stream to the archive as an Excel file
    archive.append(bufferStream, { name: `${fileName}.xlsx` });

    // Finalize the archive and close the stream
    await archive.finalize();

    // Send the zip file to the client
    ctx.response.attachment(`${fileName}.zip`); // Set the file name for download
    ctx.response.type = "application/zip"; // Set the MIME type for ZIP files

    // Stream the ZIP file to the client
    ctx.body = fs.createReadStream(zipFile);

    // Defer file deletion until the response stream is closed
    ctx.body.on("close", async () => {
      await fsPromises.unlink(zipFile).catch((err) => {
        console.error("Error deleting zip file:", err);
      });
    });
  } catch (error) {
    console.error("Error exporting data:", error);
    ctx.throw(500, "Failed to export data");
  }
};

const exportProductWebAttributeCSV = async (ctx: Context) => {
  try {
    ctx.set(
      "Content-disposition",
      "attachment; filename=product_web_attributes.csv"
    );
    ctx.set("Content-Type", "text/csv");

    const stream = createProductCsvStream();
    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    await processProductWebAttributesToStream(stream);
  } catch (err) {
    console.error("CSV Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export CSV.";
  }
};

const exportProductSpecificationsCSV = async (ctx: Context) => {
  try {
    ctx.set("Content-disposition", "attachment; filename=specifications.csv");
    ctx.set("Content-Type", "text/csv");

    const stream = createProductSpecificationCsvStream();
    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    await processProductSpecificationsToStream(stream);
  } catch (err) {
    console.error("CSV Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export CSV.";
  }
};

const exportFGControlMainText = async (ctx: Context) => {
  try {
    ctx.set("Content-disposition", "attachment; filename=fg_control_main.txt");
    ctx.set("Content-Type", "text/plain");

    const stream = createCsvStreamForFGControlMain();
    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    await processFGControlMainToStream(stream);
  } catch (err) {
    console.error("Text File Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export Text File.";
  }
};

module.exports = {
  async fgControlMains(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_CONTROL_MAIN", filters);
  },

  async fgCustomerBusinesses(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_CUSTOMER_BUSINESS", filters);
  },

  async fgCustomerInternals(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_CUSTOMER_INTERNAL", filters);
  },

  async fgProductBusinesses(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_PRODUCT_BUSINESS", filters);
  },

  async fgProductInternals(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_PRODUCT_INTERNAL", filters);
  },

  async fgRelationships(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "FG_RELATIONSHIP", filters);
  },

  async accounts(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "BUSINESS_PARTNER", {
      ...filters,
      roles: { bp_role: { $in: ["FLCU01", "FLCU00"] } },
    });
  },

  async prospects(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "BUSINESS_PARTNER", {
      ...filters,
      roles: { bp_role: { $in: ["PRO001"] } },
    });
  },

  async contacts(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "BUSINESS_PARTNER_CONTACT", {
      ...filters,
      business_partner_person: { roles: { bp_role: { $in: ["BUP001"] } } },
    });
  },

  async phoneCallActivity(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "CRM_ACTIVITY", {
      ...filters,
      document_type: { $eq: "0002" },
    });
  },

  async taskActivity(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "CRM_ACTIVITY", {
      ...filters,
      document_type: { $eq: "0006" },
    });
  },

  async opportunity(ctx) {
    const { filters } = ctx.query;
    await exportData(ctx, "CRM_OPPORTUNITY", filters);
  },

  async importFileLogs(ctx) {
    const { fileStateDocId } = ctx.params;
    const filters = { import_file_id: { $eq: fileStateDocId } };
    await exportData(ctx, "IMPORT-FILE-LOG", filters);
  },

  async cart(ctx) {
    await exportDataAsCSV(ctx);
  },

  async productWebAttribute(ctx) {
    await exportProductWebAttributeCSV(ctx);
  },

  async productSpecifications(ctx) {
    await exportProductSpecificationsCSV(ctx);
  },

  async fgControlMainText(ctx) {
    await exportFGControlMainText(ctx);
  },
};
