export default {
  /**
   * After fetching multiple entries
   */
  async afterFindMany(event: any) {
    event.result = await Promise.all(
      event.result.map(async (entry: any) => {
        if (entry.url) {
          entry.url = entry.url;//await makePublicUrl(entry.url);
        }
        return entry;
      })
    );
  },

  /**
   * After fetching a single entry
   */
  async afterFindOne(event: any) {
    if (event.result && event.result.url) {
      event.result.url = event.result.url; //await makePublicUrl(event.result.url);
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi.query("api::product-media.product-media").update({
            where: { id: result.id },
            data: {
              product: {
                connect: [product.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi.query("api::product-media.product-media").update({
            where: { id: result.id },
            data: {
              product: {
                connect: [product.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi.query("api::product-media.product-media").update({
            where: { id: result.id },
            data: {
              product: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
