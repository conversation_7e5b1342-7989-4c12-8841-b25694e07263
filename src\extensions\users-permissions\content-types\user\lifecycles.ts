export default {
  /**
   * Lifecycle function triggered before creating a user.
   */
  async beforeCreate(event) {
    const { data } = event.params;

    if (!data.username) {
      // Generate a base username from email or name
      let baseUsername: string;
      if (data.email) {
        baseUsername = data.email.split("@")[0]; // Use part before @ from email
      } else if (data.firstName && data.lastName) {
        baseUsername = `${data.firstName}_${data.lastName}`.toLowerCase();
      } else if (data.firstName) {
        baseUsername = data.firstName.toLowerCase();
      } else {
        baseUsername = `user${Date.now()}`; // Fallback to a timestamp-based username
      }

      // Ensure the username is unique
      let username = baseUsername;
      let suffix = 1;
      while (
        await strapi.db.query("plugin::users-permissions.user").findOne({
          where: { username },
        })
      ) {
        username = `${baseUsername}${suffix}`;
        suffix++;
      }

      data.username = username;
    }
  },

  /**
   * Called after a user is created.
   * Automatically creates a cart for the new user.
   */
  async afterCreate(event) {
    const { result } = event; // Contains the created user data

    if (result.id) {
      try {
        // Create a cart for the user
        await strapi.documents("api::cart.cart").create({
          data: {
            user: result.id,
          },
        });
        console.log(`Cart successfully created for user ID: ${result.id}`);
      } catch (error) {
        console.error(`Error creating cart for user ID: ${result.id}`, error);
      }
    }
  },

  /**
   * Called after a user is created.
   * Automatically creates a cart for the new user.
   */
  async afterUpdate(event) {
    const { result } = event;
    const currentStatus = result?.blocked;

    if (typeof currentStatus === 'boolean') {
      try {
        if (!currentStatus) {
          await strapi
            .plugin("email-designer-5")
            .service("email")
            .sendTemplatedEmail(
              {
                to: result.email,
              },
              {
                templateReferenceId: 3,
                subject: `Consolidated Hospitality Supplies Vendor Web Site Registration`,
              },
              {
                email: result.email,
              }
            );
        } else {
          await strapi.plugins["email"].services.email.send({
            to: result.email,
            subject: `Your Account is Now ${!currentStatus ? 'Active' : 'Inactive'}`,
            html: `
              <p>Hello ${result?.firstName || 'User'},</p>
              <p>We wanted to inform you that your account has been ${!currentStatus ? 'activated' : 'deactivated'}.</p>
              <p>If you have any questions or need further assistance, please contact our support team.</p>
              <p>Thank you.</p>
              <p>Best regards,</p>
              <p>Support Team</p>
            `,
          });
        }

      } catch (error) {
        strapi.log.error('Error sending email:', error);
      }
    }
  },

  /**
   * Called before a user is deleted.
   * Deletes the cart associated with the user.
   */
  async beforeDelete(event) {
    const { where } = event.params; // Access the ID of the user being deleted
    const userId = where.id;

    if (userId) {
      try {
        // Find the cart associated with the user
        const cart = await strapi.query("api::cart.cart").findOne({
          where: { user: userId },
        });

        if (cart) {
          // Delete the cart
          await strapi.query("api::cart.cart").delete({
            where: { id: cart.id },
          });
          console.log(`Cart successfully deleted for user ID: ${userId}`);
        }

        // Find the vendor associated with the user
        const vendor = await strapi
          .query("api::user-vendor.user-vendor")
          .findOne({
            where: { user: userId },
          });

        if (vendor) {
          // Delete the cart
          await strapi.query("api::user-vendor.user-vendor").delete({
            where: { id: vendor.id },
          });
          console.log(`Vendor successfully deleted for user ID: ${userId}`);
        }
      } catch (error) {
        console.error(`Error deleting cart for user ID: ${userId}`, error);
      }
    }
  },
};
