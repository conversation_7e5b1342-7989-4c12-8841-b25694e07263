import { factories } from "@strapi/strapi";
import { Context } from "koa";
import bcrypt from "bcryptjs";

export default factories.createCoreController(
  "api::user-vendor.user-vendor",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const { body } = ctx.request;
        const saltRounds = 10;

        const setting = await strapi.db.query("api::setting.setting").findOne({
          populate: { vendor_user_role: true },
        });

        if (!setting?.vendor_user_role?.id) {
          return ctx.throw(400, "Vendor user role not found in settings");
        }

        // ❌ Prevent duplicate emails
        const existingUser = await strapi.db
          .query("plugin::users-permissions.user")
          .findOne({
            where: { email: body.email },
          });

        if (existingUser) {
          return ctx.throw(400, "Email is already in use");
        }

        await strapi.db.transaction(async () => {
          // Genrate username if not available
          if (!body.username) {
            // Generate a base username from email or name
            let baseUsername: string;
            if (body.email) {
              baseUsername = body.email.split("@")[0]; // Use part before @ from email
            } else if (body.firstName && body.lastName) {
              baseUsername = `${body.firstName}_${body.lastName}`.toLowerCase();
            } else if (body.firstName) {
              baseUsername = body.firstName.toLowerCase();
            } else {
              baseUsername = `user${Date.now()}`; // Fallback to a timestamp-based username
            }

            // Ensure the username is unique
            let username = baseUsername;
            let suffix = 1;
            while (
              await strapi.db.query("plugin::users-permissions.user").findOne({
                where: { username },
              })
            ) {
              username = `${baseUsername}${suffix}`;
              suffix++;
            }

            body.username = username;
          }

          let hashedSecurityAns1;
          let hashedSecurityAns2;
          let password;

          // ✅ Hash security answers using bcryptjs
          if (body.security_que_1_ans) {
            hashedSecurityAns1 = await bcrypt.hash(
              body.security_que_1_ans,
              saltRounds
            );
          }

          if (body.security_que_2_ans) {
            hashedSecurityAns2 = await bcrypt.hash(
              body.security_que_2_ans,
              saltRounds
            );
          }

          if (body.password) {
            password = await bcrypt.hash(body.password, saltRounds);
          }

          // ✅ Create user entry
          const user = await strapi.db
            .query("plugin::users-permissions.user")
            .create({
              data: {
                firstname: body.firstname,
                lastname: body.lastname,
                username: body.username,
                email: body.email,
                password,
                confirmed: false,
                blocked: true,
                role: setting.vendor_user_role.id,
              },
            });

          // ✅ Create vendor entry linked to the user
          await strapi.db.query("api::user-vendor.user-vendor").create({
            data: {
              address: body?.address ?? null,
              country: body?.country ?? null,
              city: body?.city ?? null,
              zipcode: body?.zipcode ?? null,
              invoice_ref: body?.invoice_ref ?? null,
              purchase_order: body?.purchase_order ?? null,
              department: body?.department ?? null,
              phone: body?.phone ?? null,
              website: body?.website ?? null,
              vendor_id: body?.vendor_id ?? null,
              security_que_1: body?.security_que_1 ?? null,
              security_que_1_ans: hashedSecurityAns1 ?? null, // Store hashed answer
              security_que_2: body?.security_que_2 ?? null,
              security_que_2_ans: hashedSecurityAns2 ?? null, // Store hashed answer
              user: user.id,
            },
          });

          await strapi
            .plugin("email-designer-5")
            .service("email")
            .sendTemplatedEmail(
              {
                to: body.email,
              },
              {
                templateReferenceId: 2,
                subject: `Thank you for registering with Consolidated Hospitality Supplies Vendor Web Site!`,
              },
              {}
            );

          if (
            Array.isArray(setting?.vendor_admin_user_emails) &&
            setting?.vendor_admin_user_emails.length > 0
          ) {
            await strapi
              .plugin("email-designer-5")
              .service("email")
              .sendTemplatedEmail(
                {
                  to: setting.vendor_admin_user_emails,
                },
                {
                  templateReferenceId: 1,
                  subject: `New User Registration for Vendor Portal`,
                },
                {
                  name: body.firstname + " " + body.lastname,
                  email: body.email,
                  phone: body.phone,
                  vendorId: body.vendor_id ? "Vendor Id:" + body.vendor_id : "",
                  invoiceRef: body.invoice_ref
                    ? "Invoice Ref:" + body.invoice_ref
                    : "",
                  purchaseOrder: body.purchase_order
                    ? "Purchase Order:" + body.purchase_order
                    : "",
                }
              );
          }

          return ctx.send({ message: "Registration successfully done" });
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to process request: ${error.message}`
        );
      }
    },

    async forgotPassword(ctx: Context) {
      try {
        const {
          email,
          security_que_1,
          security_que_1_ans,
          security_que_2,
          security_que_2_ans,
        } = ctx.request.body;

        if (
          !email ||
          !security_que_1 ||
          !security_que_1_ans ||
          !security_que_2 ||
          !security_que_2_ans
        ) {
          return ctx.throw(400, "All fields are required");
        }

        // ✅ Find user vendor by email and security question IDs
        const userVendor = await strapi.db
          .query("api::user-vendor.user-vendor")
          .findOne({
            where: { user: { email }, security_que_1, security_que_2 },
            populate: { user: true },
          });

        if (!userVendor) {
          return ctx.throw(400, "Invalid email or security questions");
        }

        // ✅ Validate security answers using bcryptjs
        const isAnswer1Valid = await bcrypt.compare(
          security_que_1_ans,
          userVendor.security_que_1_ans
        );
        const isAnswer2Valid = await bcrypt.compare(
          security_que_2_ans,
          userVendor.security_que_2_ans
        );

        if (!isAnswer1Valid || !isAnswer2Valid) {
          return ctx.throw(400, "Security answers do not match");
        }

        // Access the user directly from userVendor
        const user = userVendor.user;

        // ✅ Generate Strapi reset password token
        const resetPasswordToken = strapi.plugins[
          "users-permissions"
        ].services.jwt.issue({
          id: user.id,
        });

        if (!resetPasswordToken) {
          return ctx.throw(500, "Failed to generate reset token");
        }

        // ✅ Store the reset token on the user (in the users table)
        await strapi.db.query("plugin::users-permissions.user").update({
          where: { id: user.id },
          data: { resetPasswordToken },
        });

        // ✅ Send reset password email
        await strapi.plugins["email"].services.email.send({
          to: email,
          subject: "Password Reset Request",
          html: `
            <p>Hello,</p>
            <p>You have requested to reset your password.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="${process.env.PUBLIC_URL}/#/auth/reset-password?code=${resetPasswordToken}" target="_blank">Reset Password</a></p>
            <p>If you did not request this, please ignore this email.</p>
            <p>Thank you.</p>
          `,
        });

        return ctx.send({ message: "Password reset email sent successfully" });
      } catch (error) {
        console.error("Password reset error:", error);
        return ctx.internalServerError(
          `Failed to process request: ${error.message}`
        );
      }
    },
  })
);
