import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
    secretAccessKey: process.env.AWS_ACCESS_SECRET as string,
  },
});

/**
 * Checks if the given URL matches the expected S3 URL format for the configured bucket and region.
 * @param url - The URL to check.
 * @returns True if the URL is valid for the configured S3 bucket and region, false otherwise.
 */
const isAwsS3Url = (url: string): boolean => {
  const expectedUrl = `${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com`;

  // Check if the URL starts with the expected S3 URL pattern
  return url.startsWith(`https://${expectedUrl}`);
};

/**
 * Extracts the S3 object key from a full URL.
 * @param url - The full S3 URL.
 * @returns The object key as a string.
 */
const extractObjectKey = (url: string): string | null => {
  try {
    const urlObject = new URL(url); // Parse the URL
    const hostname = `${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com`;

    if (!urlObject.hostname.includes(hostname)) {
      console.error(
        "The URL is not a valid AWS S3 URL for the configured bucket and region."
      );
      return null;
    }

    // Remove the leading slash from pathname
    return decodeURIComponent(urlObject.pathname.slice(1));
  } catch (err) {
    console.error("Error parsing URL:", err);
    return null;
  }
};

/**
 * Generates a signed public URL for an S3 object.
 * @param url - AWS image URL.
 * @returns A signed URL string.
 */
const makePublicUrl = async (url: string): Promise<string | null> => {
  try {
    if (!isAwsS3Url(url)) {
      console.error(
        "The URL is not a valid AWS S3 URL for the configured bucket and region."
      );
      return url;
    }

    // Extract the object key from the stored URL
    const objectKey = extractObjectKey(url);

    if (!objectKey) {
      console.error("Failed to extract object key from URL.");
      return url;
    }

    const command = new GetObjectCommand({
      Bucket: process.env.AWS_BUCKET as string,
      Key: objectKey,
    });

    // Generate the signed URL
    // Expiry time for the signed URL in seconds (default: 3600 seconds or 1 hour).
    return await getSignedUrl(s3Client, command, { expiresIn: 900 });
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return url; // Return null if there's an error
  }
};

export { makePublicUrl };
