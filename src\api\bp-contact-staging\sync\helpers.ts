const BusinessPartner = async (data) => {
  const { bp_id, locale } = data;

  if (!bp_id) throw new Error("bp_id is required");

  try {
    // Check if the business partner already exists by bp_id with locale
    const existingPartner = await strapi.db
      .query("api::business-partner.business-partner")
      .findOne({
        where: { bp_id, locale },
        populate: {
          roles: true,
        },
      });

    if (existingPartner) {
      // Update the existing business partner
      await strapi.db.query("api::business-partner.business-partner").update({
        where: { documentId: existingPartner.documentId },
        data,
      });
    } else {
      // Create a new business partner
      await strapi.db.query("api::business-partner.business-partner").create({
        data,
      });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize BusinessPartner ::: ${error.message}`
    );
  }
};

const BusinessPartnerExtension = async (data) => {
  const { bp_id, locale } = data;

  if (!bp_id) throw new Error("bp_id is required");

  try {
    // Check if the business partner extension already exists by bp_id with locale
    const existingPartner = await strapi.db
      .query("api::business-partner-extension.business-partner-extension")
      .findOne({
        where: { bp_id, locale },
        populate: {
          roles: true,
        },
      });

    if (existingPartner) {
      // Update the existing business partner extension
      await strapi.db
        .query("api::business-partner-extension.business-partner-extension")
        .update({
          where: { documentId: existingPartner.documentId },
          data,
        });
    } else {
      // Create a new business partner extension
      await strapi.db
        .query("api::business-partner-extension.business-partner-extension")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize BusinessPartnerExtension ::: ${error.message}`
    );
  }
};

const BusinessPartnerContact = async (data) => {
  const {
    relationship_number,
    bp_person_id,
    bp_company_id,
    validity_end_date,
    locale,
  } = data;

  if (
    !relationship_number ||
    !bp_person_id ||
    !bp_company_id ||
    !validity_end_date
  ) {
    throw new Error(
      "relationship_number, bp_person_id, bp_company_id and validity_end_date are required"
    );
  }

  try {
    // Check if the business partner contact already exists
    const existingContact = await strapi.db
      .query("api::business-partner-contact.business-partner-contact")
      .findOne({
        where: {
          relationship_number,
          bp_person_id,
          bp_company_id,
          validity_end_date,
          locale,
        },
      });

    if (existingContact) {
      // Update the existing business partner contact
      await strapi.db
        .query("api::business-partner-contact.business-partner-contact")
        .update({
          where: { documentId: existingContact.documentId },
          data,
        });
    } else {
      // Create a new business partner contact
      await strapi.db
        .query("api::business-partner-contact.business-partner-contact")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Contact ::: ${error.message}`
    );
  }
};

const BusinessPartnerAddress = async (data) => {
  const { bp_id, bp_address_id, locale } = data;

  if (!bp_id || !bp_address_id)
    throw new Error("bp_id && bp_address_id is required");

  try {
    // Check if the business partner address already exists by bp_address_id with locale
    let address = await strapi.db
      .query("api::business-partner-address.business-partner-address")
      .findOne({
        where: { bp_id, bp_address_id, locale },
      });

    const {
      email_addresses,
      fax_numbers,
      phone_numbers,
      mobile_phone_numbers,
      ...addressData
    } = data;

    if (address) {
      // Update the existing business partner address
      address = await strapi.db
        .query("api::business-partner-address.business-partner-address")
        .update({
          where: { documentId: address.documentId },
          data: addressData,
        });
    } else {
      // Create a new business partner address
      address = await strapi.db
        .query("api::business-partner-address.business-partner-address")
        .create({
          data: addressData,
        });
    }

    // Sync Business Partner Email Address Data
    if (Array.isArray(email_addresses)) {
      for (const email of email_addresses) {
        if (!email?.locale) {
          email.locale = locale;
        }
        email.business_partner_address = address.id;
        await BusinessPartnerEmailAddress(email);
      }
    }

    // Sync Business Partner Fax Number Data
    if (Array.isArray(fax_numbers)) {
      for (const fax of fax_numbers) {
        if (!fax?.locale) {
          fax.locale = locale;
        }
        fax.business_partner_address = address.id;
        await BusinessPartnerFaxNumber(fax);
      }
    }

    // Sync Business Partner Phone Number Data
    if (Array.isArray(phone_numbers)) {
      for (const phone of phone_numbers) {
        if (!phone?.locale) {
          phone.locale = locale;
        }
        phone.business_partner_address = address.id;
        await BusinessPartnerPhoneNumber(phone);
      }
    }

    // Sync Business Partner Mobile Phone Number Data
    if (Array.isArray(mobile_phone_numbers)) {
      for (const mobile of mobile_phone_numbers) {
        if (!mobile?.locale) {
          mobile.locale = locale;
        }
        mobile.business_partner_address = address.id;
        await BusinessPartnerPhoneNumber(mobile);
      }
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Address ::: ${error.message}`
    );
  }
};

const BusinessPartnerContactToFuncAndDepts = async (data) => {
  const {
    relationship_number,
    bp_person_id,
    bp_company_id,
    validity_end_date,
    locale,
  } = data;

  if (
    !relationship_number ||
    !bp_person_id ||
    !bp_company_id ||
    !validity_end_date
  ) {
    throw new Error(
      "bp_contact_address_id, relationship_number, bp_person_id, bp_company_id and validity_end_date are required"
    );
  }

  try {
    // Check if the business partner contact function & department exists
    const existingContactFuncDept = await strapi.db
      .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
      .findOne({
        where: {
          relationship_number,
          bp_person_id,
          bp_company_id,
          validity_end_date,
          locale,
        },
      });

    if (existingContactFuncDept) {
      // Update the existing contact function & department
      await strapi.db
        .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
        .update({
          where: { documentId: existingContactFuncDept.documentId },
          data,
        });
    } else {
      // Create a new contact function & department
      await strapi.db
        .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Contact Contact Function & Department ::: ${error.message}`
    );
  }
};

const BusinessPartnerRole = async (data) => {
  const { bp_role, bp_id, locale } = data;

  if (!bp_id || !bp_role) throw new Error("bp_id and bp_role are required");

  try {
    // Check if the business partner role exists
    const existingRole = await strapi.db
      .query("api::business-partner-role.business-partner-role")
      .findOne({
        where: { bp_id, bp_role, locale },
      });

    if (existingRole) {
      // Update the existing business partner role
      await strapi.db
        .query("api::business-partner-role.business-partner-role")
        .update({
          where: { id: existingRole.id },
          data,
        });
    } else {
      // Create a new business partner role
      await strapi.db
        .query("api::business-partner-role.business-partner-role")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Role ::: ${error.message}`
    );
  }
};

const BusinessPartnerEmailAddress = async (data) => {
  const { email_address, business_partner_address, locale } = data;

  if (!email_address) {
    throw new Error("email_address is required");
  }

  try {
    // Check if the Business Partner Email Address exists
    const existingRecord = await strapi.db
      .query("api::bp-email-address.bp-email-address")
      .findOne({
        where: {
          business_partner_address,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-email-address.bp-email-address").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-email-address.bp-email-address")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Email Address ::: ${error.message}`
    );
  }
};

const BusinessPartnerPhoneNumber = async (data) => {
  const { phone_number, phone_number_type, business_partner_address, locale } =
    data;

  if (!phone_number || !phone_number_type) {
    throw new Error("phone_number and phone_number_type are required");
  }

  try {
    // Check if the Business Partner Phone Number exists
    const existingRecord = await strapi.db
      .query("api::bp-phone-number.bp-phone-number")
      .findOne({
        where: {
          phone_number_type,
          business_partner_address,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-phone-number.bp-phone-number").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-phone-number.bp-phone-number")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Phone Number ::: ${error.message}`
    );
  }
};

const BusinessPartnerFaxNumber = async (data) => {
  const { fax_number, business_partner_address, locale } = data;

  if (!fax_number) {
    throw new Error("fax_number is required");
  }

  try {
    // Check if the Business Partner Fax Number exists
    const existingRecord = await strapi.db
      .query("api::bp-fax-number.bp-fax-number")
      .findOne({
        where: {
          business_partner_address,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-fax-number.bp-fax-number").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-fax-number.bp-fax-number")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Fax Number ::: ${error.message}`
    );
  }
};

export {
  BusinessPartner,
  BusinessPartnerExtension,
  BusinessPartnerContact,
  BusinessPartnerAddress,
  BusinessPartnerContactToFuncAndDepts,
  BusinessPartnerRole,
  BusinessPartnerEmailAddress,
  BusinessPartnerPhoneNumber,
};
