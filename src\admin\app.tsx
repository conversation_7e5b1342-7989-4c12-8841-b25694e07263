import type { StrapiApp } from "@strapi/strapi/admin";
import { Store } from "@strapi/icons";
import { checkRole, setupStorageEventListeners, STORAGE_KEYS } from "./auth";
// @ts-ignore
import AuthLogo from "./images/auth-logo.png";
// @ts-ignore
import <PERSON><PERSON><PERSON><PERSON> from "./images/menu-logo.png";

export default {
  config: {
    // Replace the Strapi logo in auth (login) views
    auth: {
      logo: AuthLogo,
    },
    // Replace the Strapi logo in the main navigation
    menu: {
      logo: <PERSON>u<PERSON><PERSON>,
    },
    // replace favicon with a custom icon
    head: {
      favicon: MenuLogo,
    },
    locales: [
      // 'ar',
      // 'fr',
      // 'cs',
      // 'de',
      // 'dk',
      // 'es',
      // 'he',
      // 'id',
      // 'it',
      // 'ja',
      // 'ko',
      // 'ms',
      // 'nl',
      // 'no',
      // 'pl',
      // 'pt-BR',
      // 'pt',
      // 'ru',
      // 'sk',
      // 'sv',
      // 'th',
      // 'tr',
      // 'uk',
      // 'vi',
      // 'zh-<PERSON>',
      // 'zh',
    ],
  },
  bootstrap(app: StrapiApp) {
    // console.log(app);
    // Listen for token storage updates
    window.addEventListener("storageChange", (event: any) => {
      const { key, value, storageType } = event.detail;
      if (key === STORAGE_KEYS.TOKEN && value) {
        console.log(`JWT token updated in ${storageType}:`);
        setTimeout(() => {
          checkRole(storageType);
        }, 100);
      }
    });
  },
  register(app: StrapiApp) {
    // console.log(app);
    // Ensure storage event listener is set up
    setupStorageEventListeners();

    // Store front link in menu
    app.addMenuLink({
      to: "/store",
      icon: Store,
      intlLabel: {
        id: "store.plugin.name",
        defaultMessage: "Store",
      },
      Component: async () => {
        return {
          default: () => {
            // Perform any redirection logic if necessary
            window.location.href = "/index.html"; // Redirect to your desired URL
            return null; // Render nothing
          },
        };
      },
      permissions: [],
      position: -1,
    });
  },
};
