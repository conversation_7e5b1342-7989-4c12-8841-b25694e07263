import { generateUniqueID } from "../../../../utils/cpi";

export default {
  async beforeCreate(event) {
    const { params } = event;
    if (!params.data.opportunity_id) {
      try {
        const customId = `${await generateUniqueID("crm_opportunity_id_seq")}`;
        params.data.opportunity_id = customId;
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.prospect_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.prospect_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.primary_contact_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.primary_contact_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.main_employee_responsible_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_employee_responsible_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.prospect_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.prospect_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.prospect_party_id &&
          result?.business_partner?.count === 1
        ) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.primary_contact_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.primary_contact_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.primary_contact_party_id &&
          result?.business_partner_contact?.count === 1
        ) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.main_employee_responsible_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_employee_responsible_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.main_employee_responsible_party_id &&
          result?.business_partner_owner?.count === 1
        ) {
          await strapi.query("api::crm-opportunity.crm-opportunity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
