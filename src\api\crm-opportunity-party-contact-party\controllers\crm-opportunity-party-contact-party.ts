/**
 * crm-opportunity-party-contact-party controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { generateUniqueID, ReplicateBPContactHupSpot, ReplicateBusinessPartnerContact } from "../../../utils/cpi";

export default factories.createCoreController(
  "api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const {
          first_name,
          middle_name,
          last_name,
          contact_person_department,
          contact_person_department_name,
          contact_person_function,
          contact_person_function_name,
          contact_person_vip_type,
          job_title,
          business_department,
          best_reached_by,
          web_registered,
          emails_opt_in,
          print_marketing_opt_in,
          sms_promotions_opt_in,
          prfrd_comm_medium_type,
          purchasing_control,
          native_language,
          bp_id,
          opportunity_party_contact_main_indicator,
          role_code,
          opportunity_id,
        } = ctx.request.body;

        if (!bp_id || !opportunity_id) {
          return ctx.throw(400, "bp_id and opportunity_id are required");
        }

        const locale = "en";

        const newBP = await strapi.db.transaction(async () => {
          const contact_bp_id = `S${await generateUniqueID("bp_contact_id_seq")}`;
          let data: any = {
            bp_id: contact_bp_id,
            bp_full_name: [first_name, last_name]
              .filter(Boolean)
              .join(" ")
              .trim(),
            first_name,
            middle_name,
            last_name,
            locale,
          };

          // Create Business Partner Person
          const bp_person = await strapi
            .query("api::business-partner.business-partner")
            .create({ data });

          // Find BP extension
          const bpExtension = await strapi.db
            .query("api::business-partner-extension.business-partner-extension")
            .findOne({
              where: { bp_id: bp_person.bp_id },
            });

          if (bpExtension) {
            // **Update BP extension**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .update({
                where: { id: bpExtension.id },
                data: {
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                },
              });
          } else {
            // **Create BP extension if not exists**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .create({
                data: {
                  bp_id: bp_person.bp_id,
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                  locale: bp_person.locale,
                },
              });
          }

          // Assign a BP Role
          data = {
            bp_role: "BUP001",
            bp_id: bp_person.bp_id,
            locale,
          };

          await strapi
            .query("api::business-partner-role.business-partner-role")
            .create({ data });

          // **Find or Create Business Partner Relationship**
          let bp_relationship = await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .findOne({
              where: {
                bp_id1: bp_id,
                bp_id2: bp_person.bp_id,
              },
            });

          if (!bp_relationship) {
            data = {
              relationship_number: `REL${Date.now()}`, // Generate a unique relationship number
              bp_id1: bp_id, // Company
              bp_id2: bp_person.bp_id, // Contact
              validity_start_date: new Date(),
              validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
              locale,
            };

            bp_relationship = await strapi
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .create({ data });
          }

          // Create BP Contact linked to Relationship
          data = {
            bp_person_id: bp_person.bp_id,
            bp_company_id: bp_id,
            relationship_number: bp_relationship.relationship_number,
            relationship: bp_relationship.id, // Link to relationship
            validity_start_date: new Date(),
            validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
            locale,
          };

          const bpContact = await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .create({ data });

          // **Insert Department & Function**
          if (
            contact_person_department ||
            contact_person_function ||
            contact_person_vip_type
          ) {
            data = {
              bp_person_id: bp_person.bp_id,
              bp_company_id: bp_id,
              relationship_number: bp_relationship.relationship_number,
              relationship: bp_relationship.id, // Link to relationship
              validity_start_date: new Date(),
              validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
              locale,
            };

            if (contact_person_department) {
              data.contact_person_department = contact_person_department;
              data.contact_person_department_name =
                contact_person_department_name;
            }

            if (contact_person_function) {
              data.contact_person_function = contact_person_function;
              data.contact_person_function_name = contact_person_function_name;
            }

            // If VIP is true, reset others first
            if (contact_person_vip_type === true) {
              await strapi.db
                .query(
                  "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
                )
                .updateMany({
                  where: {
                    bp_company_id: bp_id,
                    contact_person_vip_type: true,
                  },
                  data: { contact_person_vip_type: false },
                });
            }

            if (typeof contact_person_vip_type === "boolean") {
              data.contact_person_vip_type = contact_person_vip_type;
            }

            await strapi
              .query(
                "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
              )
              .create({ data });
          }

          // Find Default Address
          const address = await strapi.db
            .query("api::business-partner-address.business-partner-address")
            .findOne({
              where: {
                bp_id,
                address_usages: {
                  address_usage: "XXDEFAULT",
                },
              },
            });

          // Create Contact Address linked to Relationship
          data = {
            house_number: address?.house_number,
            additional_street_prefix_name:
              address?.additional_street_prefix_name,
            additional_street_suffix_name:
              address?.additional_street_suffix_name,
            street_name: address?.street_name,
            city_name: address?.city_name,
            country: address?.country,
            county_code: address?.county_code,
            postal_code: address?.postal_code,
            region: address?.region,
            validity_start_date: new Date(),
            validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
            bp_id: bp_person.bp_id,
            prfrd_comm_medium_type,
            locale,
          };

          const contact_address = await strapi
            .query("api::business-partner-address.business-partner-address")
            .create({ data });

          // Create Contact Info (Email, Phone, Fax, Website) linked to Relationship
          const contactInfoTypes = [
            {
              key: "email_address",
              table: "bp-email-address.bp-email-address",
            },
            { key: "fax_number", table: "bp-fax-number.bp-fax-number" },
            { key: "website_url", table: "bp-home-page-url.bp-home-page-url" },
            { key: "phone_number", table: "bp-phone-number.bp-phone-number" },
            { key: "mobile", table: "bp-phone-number.bp-phone-number" },
          ];

          for (const info of contactInfoTypes) {
            if (ctx.request.body[info.key]) {
              if (["phone_number", "mobile"].includes(info.key)) {
                data = {
                  phone_number: ctx.request.body[info.key],
                  destination_location_country: ctx?.request?.body?.destination_location_country,
                  phone_number_type: info.key === "phone_number" ? "1" : "3",
                  business_partner_address: {
                    connect: [contact_address.id],
                  },
                  locale,
                };
                const t: any = `api::${info.table}`;
                await strapi.query(t).create({ data });
              } else if (!["phone_number", "mobile"].includes(info.key)) {
                data = {
                  [info.key]: ctx.request.body[info.key],
                  business_partner_address: {
                    connect: [contact_address.id],
                  },
                  locale,
                };
                const t: any = `api::${info.table}`;
                await strapi.query(t).create({ data });
              }
            }
          }

          data = {
            opportunity_party_contact_party_id: bp_person.bp_id,
            opportunity_party_contact_main_indicator,
            role_code,
            opportunity_id,
            locale,
          };

          await strapi
            .query(
              "api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party"
            )
            .create({ data });

          setImmediate(async () => {
            await ReplicateBusinessPartnerContact(bp_id);
            // await ReplicateBPContactHupSpot(bpContact.bp_person_id);
          });

          return bpContact;
        });

        return ctx.send({
          message: "Business partner contact registered successfully",
          data: newBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Business partner contact: ${error.message}`
        );
      }
    },
  })
);
