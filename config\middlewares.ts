export default [
  "strapi::logger",
  "strapi::errors",
  {
    name: "strapi::security",
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          "connect-src": ["'self'", "http:", "https:"],
          "default-src": [
            "'self'",
            "http:",
            "https:",
            `${process.env.CF_PUBLIC_ACCESS_URL ? process.env.CF_PUBLIC_ACCESS_URL.replace(/^https?:\/\//, "") : ""}`,
          ],
          "img-src": [
            "'self'",
            "data:",
            "blob:",
            `${process.env.CF_PUBLIC_ACCESS_URL ? process.env.CF_PUBLIC_ACCESS_URL.replace(/^https?:\/\//, "") : ""}`,
          ],
          "media-src": [
            "'self'",
            "data:",
            "blob:",
            `${process.env.CF_PUBLIC_ACCESS_URL ? process.env.CF_PUBLIC_ACCESS_URL.replace(/^https?:\/\//, "") : ""}`,
          ],
          "script-src": ["'self'", "data:", "blob:", 'unsafe-inline', "editor.unlayer.com"],
          "script-src-attr": ["'unsafe-inline'"],
          "frame-src": ["'self'", "editor.unlayer.com"],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  "strapi::cors",
  "strapi::poweredBy",
  "strapi::query",
  "strapi::body",
  "strapi::session",
  "strapi::favicon",
  {
    name: "strapi::public",
    config: {
      maxAge: 3600000 * 2, // 2 hour cache milliseconds
    },
  },
  {
    name: 'strapi::compression',
    config: {
      gzip: true
    },
  },
  { resolve: "./src/middlewares/redirect" },
];
