import axios from "axios";

export const STORAGE_KEYS = {
  TOKEN: "jwtToken",
  USER: "userInfo",
};

export const getStoredToken = (): string | null => {
  const token =
    localStorage.getItem(STORAGE_KEYS.TOKEN) ??
    sessionStorage.getItem(STORAGE_KEYS.TOKEN);

  if (typeof token === "string") {
    return JSON.parse(token);
  }

  return null;
};

const setUserData = (storageType: string, data: any): void => {
  try {
    if (storageType === "sessionStorage") {
      sessionStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(data));
    } else {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(data));
    }
  } catch (error) {
    console.log(`USER data updated in ${storageType} error:`, error);
  }
};

// Utility to override setItem for storage tracking
export const setupStorageEventListeners = () => {
  const originalSetItem = localStorage.setItem;
  const originalSessionSetItem = sessionStorage.setItem;

  const emitStorageEvent = (
    key: string,
    value: string | null,
    storageType: string
  ) => {
    const event = new CustomEvent("storageChange", {
      detail: { key, value, storageType },
    });
    window.dispatchEvent(event);
  };

  // Override localStorage.setItem
  localStorage.setItem = function (key: string, value: string): void {
    emitStorageEvent(key, value, "localStorage");
    originalSetItem.call(this, key, value);
  };

  // Override sessionStorage.setItem
  sessionStorage.setItem = function (key: string, value: string): void {
    emitStorageEvent(key, value, "sessionStorage");
    originalSessionSetItem.call(this, key, value);
  };
};

let isProcessing = false;

export const checkRole = async (storageType: string) => {
  try {
    if (isProcessing) {
      return;
    }
    isProcessing = true;
    const response = await axios.get("/admin/users/me", {
      baseURL: "/",
      headers: {
        Authorization: `Bearer ${getStoredToken()}`,
      },
    });
    isProcessing = false;
    const { data } = response?.data || null;
    if (data) {
      data.isAdmin = true;
      setUserData(storageType, data);
      const { roles } = data;
      const roleCodes = [
        "strapi-super-admin",
        "strapi-editor",
        "strapi-author",
      ];
      const hasMatchingCode = roles.some((role: { code: string }) =>
        roleCodes.includes(role.code)
      );
      if (!hasMatchingCode) {
        window.location.href = "/index.html";
      }
    }
  } catch (error: any) {
    console.error(
      "Error fetching user data:",
      error.response?.data || error.message
    );
  }
};
